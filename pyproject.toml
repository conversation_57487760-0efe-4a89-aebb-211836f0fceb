[project]
name = "windows-use"
version = "0.3.5"
description = "An AI Agent that interacts with Windows OS at GUI level."
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
readme = "README.md"
license = 'MIT'
license-files = ["LICENSE"]
urls = { homepage = "https://github.com/CursorTouch" }
keywords = ["windows", "agent", "ai", "desktop","ai agent","automation"]
requires-python = ">=3.13"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: User Interfaces",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3.13",
]

dependencies = [
    "coverage>=7.9.1",
    "fuzzywuzzy>=0.18.0",
    "humancursor>=1.1.5",
    "ipykernel>=6.29.5",
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "langchain-google-genai>=2.1.5",
    "langchain-groq>=0.3.4",
    "langchain-ollama>=0.3.3",
    "langchain-openai>=0.3.27",
    "live-inspect==0.1.1",
    "markdownify>=1.1.0",
    "pillow>=11.2.1",
    "psutil>=7.0.0",
    "pyautogui>=0.9.54",
    "pydantic>=2.11.7",
    "pytest-cov>=6.2.1",
    "python-levenshtein>=0.27.1",
    "pyuac>=0.0.3",
    "pywin32>=311",
    "requests>=2.32.4",
    "rich>=14.0.0",
    "setuptools>=80.9.0",
    "termcolor>=3.1.0",
    "twine>=6.1.0",
    "uiautomation>=2.0.28",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "ruff>=0.12.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
packages = ["windows_use"]
