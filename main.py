from langchain_google_genai import ChatGoogleGenerativeAI
# from langchain_groq import ChatGroq
from windows_use.agent import Agent
from dotenv import load_dotenv
import os
import sys

load_dotenv()

def main_cli():
    """Interfaz de línea de comandos"""
    llm=ChatGoogleGenerativeAI(model='gemini-2.0-flash')
    # llm=ChatGroq(model='meta-llama/llama-4-scout-17b-16e-instruct',api_key=os.getenv("GROQ_API_KEY"))
    agent = Agent(llm=llm,browser='chrome',use_vision=False)
    
    print("🪟 Windows-Use AI Assistant (CLI)")
    print("Escribe 'quit' o 'exit' para salir\n")
    
    while True:
        try:
            query = input("Tu consulta: ")
            if query.lower() in ['quit', 'exit', 'salir']:
                print("¡Hasta luego!")
                break
            if query.strip():
                agent.print_response(query)
                print("-" * 50)
        except KeyboardInterrupt:
            print("\n¡Hasta luego!")
            break

def main_gui():
    """Interfaz gráfica"""
    try:
        from gui_widget import FloatingWidget
        app = FloatingWidget()
        app.run()
    except ImportError:
        print("Error: No se pudo importar la interfaz gráfica.")
        print("Ejecutando en modo CLI...")
        main_cli()

def main():
    """Función principal que permite elegir la interfaz"""
    if len(sys.argv) > 1:
        if sys.argv[1] == '--gui':
            main_gui()
        elif sys.argv[1] == '--cli':
            main_cli()
        else:
            print("Uso: python main.py [--gui|--cli]")
            print("Sin argumentos se ejecuta en modo CLI")
            main_cli()
    else:
        # Por defecto, mostrar opciones
        print("🪟 Windows-Use AI Assistant")
        print("Elige el modo de ejecución:")
        print("1. Interfaz gráfica (GUI)")
        print("2. Línea de comandos (CLI)")
        
        while True:
            choice = input("Selecciona (1/2): ").strip()
            if choice == '1':
                main_gui()
                break
            elif choice == '2':
                main_cli()
                break
            else:
                print("Por favor, selecciona 1 o 2")

if __name__ == "__main__":
    main()