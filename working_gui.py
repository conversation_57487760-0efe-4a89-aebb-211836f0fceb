import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import os
import sys
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class WorkingFloatingWidget:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windows-Use AI Assistant")
        self.root.geometry("450x650")
        self.root.attributes('-topmost', True)
        
        # Intentar inicializar el agente real
        self.agent = None
        self.init_agent()
        
        self.setup_ui()
        
    def init_agent(self):
        """Inicializar el agente real de Windows-Use"""
        try:
            # Intentar importar y configurar el agente
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            # Verificar si tenemos la API key
            if not os.getenv("GOOGLE_API_KEY"):
                self.add_system_message("⚠️ GOOGLE_API_KEY no encontrada en el archivo .env")
                return
                
            # Configurar el modelo
            llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash')
            
            # Intentar importar el agente local
            try:
                # Agregar el directorio actual al path para importar windows_use local
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                
                # Importar usando el código local (sin live_inspect)
                from windows_use_simple import SimpleAgent
                self.agent = SimpleAgent(llm=llm, browser='chrome', use_vision=False)
                self.agent_status = "✅ Agente Windows-Use inicializado correctamente"
                
            except ImportError as e:
                # Si no funciona el local, usar el instalado
                from windows_use.agent import Agent
                self.agent = Agent(llm=llm, browser='chrome', use_vision=False)
                self.agent_status = "✅ Agente Windows-Use inicializado (versión instalada)"
                
        except Exception as e:
            self.agent_status = f"❌ Error al inicializar agente: {str(e)}"
            self.agent = None
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="🪟 Windows-Use AI Assistant", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Área de respuestas (scrollable)
        response_frame = ttk.LabelFrame(main_frame, text="Conversación", padding="5")
        response_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        response_frame.columnconfigure(0, weight=1)
        response_frame.rowconfigure(0, weight=1)
        
        self.response_text = scrolledtext.ScrolledText(
            response_frame, 
            wrap=tk.WORD, 
            height=20,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg='#212529'
        )
        self.response_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Frame para entrada de texto
        input_frame = ttk.LabelFrame(main_frame, text="Tu consulta", padding="5")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        # Campo de entrada
        self.query_entry = tk.Text(input_frame, height=4, wrap=tk.WORD, font=('Arial', 10))
        self.query_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        self.query_entry.bind('<Control-Return>', self.send_query)
        
        # Frame para botones
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        
        # Botón enviar
        self.send_button = ttk.Button(
            button_frame, 
            text="🚀 Enviar (Ctrl+Enter)", 
            command=self.send_query
        )
        self.send_button.grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        # Botón limpiar
        clear_button = ttk.Button(
            button_frame, 
            text="🧹 Limpiar", 
            command=self.clear_responses
        )
        clear_button.grid(row=0, column=1, padx=(5, 5))
        
        # Checkbox para mantener siempre visible
        self.topmost_var = tk.BooleanVar(value=True)
        topmost_check = ttk.Checkbutton(
            button_frame,
            text="📌 Siempre visible",
            variable=self.topmost_var,
            command=self.toggle_topmost
        )
        topmost_check.grid(row=0, column=2, padx=(10, 0))
        
        # Status bar
        self.status_var = tk.StringVar(value="Inicializando...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              font=('Arial', 8), foreground='gray')
        status_bar.grid(row=3, column=0, sticky=tk.W, pady=(5, 0))
        
        # Mensaje inicial
        self.add_system_message()
        
    def add_system_message(self):
        """Agregar mensaje inicial del sistema"""
        if hasattr(self, 'agent_status'):
            status_msg = self.agent_status
        else:
            status_msg = "🔄 Inicializando agente..."
            
        initial_msg = f"¡Hola! 👋 Soy tu asistente de Windows-Use.\n\n"
        initial_msg += f"Estado: {status_msg}\n\n"
        
        if self.agent:
            initial_msg += "✅ ¡Listo para automatizar tareas en Windows!\n\n"
            initial_msg += "Puedo ayudarte con:\n"
            initial_msg += "• Abrir aplicaciones y sitios web\n"
            initial_msg += "• Navegar por páginas web\n"
            initial_msg += "• Hacer clic en botones y enlaces\n"
            initial_msg += "• Rellenar formularios\n"
            initial_msg += "• Buscar y añadir productos al carrito\n"
            initial_msg += "• Ejecutar comandos del sistema\n\n"
            initial_msg += "Ejemplo: 'Abre Chrome, ve a mercadona.es, pon código postal 17116 y busca azúcar, sal y leche'\n\n"
            self.status_var.set("✅ Listo para recibir consultas")
        else:
            initial_msg += "❌ No se pudo inicializar el agente completamente.\n"
            initial_msg += "Verifica que tengas:\n"
            initial_msg += "• GOOGLE_API_KEY en tu archivo .env\n"
            initial_msg += "• Todas las dependencias instaladas\n"
            initial_msg += "• Python 3.13+ (recomendado)\n\n"
            self.status_var.set("❌ Error en inicialización")
            
        self.add_response(initial_msg, is_system=True)
        
    def toggle_topmost(self):
        self.root.attributes('-topmost', self.topmost_var.get())
        
    def add_response(self, text, is_user=False, is_system=False):
        """Agregar texto al área de respuestas"""
        self.response_text.config(state=tk.NORMAL)
        
        if is_user:
            self.response_text.insert(tk.END, f"\n👤 Tú:\n{text}\n")
            self.response_text.insert(tk.END, "─" * 50 + "\n")
        elif is_system:
            self.response_text.insert(tk.END, f"🤖 Sistema:\n{text}\n")
            self.response_text.insert(tk.END, "═" * 50 + "\n")
        else:
            self.response_text.insert(tk.END, f"🤖 Agente:\n{text}\n")
            self.response_text.insert(tk.END, "═" * 50 + "\n")
        
        self.response_text.config(state=tk.DISABLED)
        self.response_text.see(tk.END)
        
    def clear_responses(self):
        """Limpiar el área de respuestas"""
        self.response_text.config(state=tk.NORMAL)
        self.response_text.delete(1.0, tk.END)
        self.response_text.config(state=tk.DISABLED)
        self.add_system_message()
        
    def send_query(self, event=None):
        """Enviar consulta al agente"""
        query = self.query_entry.get(1.0, tk.END).strip()
        
        if not query:
            messagebox.showwarning("Advertencia", "Por favor, escribe una consulta.")
            return
            
        if not self.agent:
            messagebox.showerror("Error", "El agente no está inicializado. Verifica la configuración.")
            return
            
        # Mostrar la consulta del usuario
        self.add_response(query, is_user=True)
        
        # Limpiar el campo de entrada
        self.query_entry.delete(1.0, tk.END)
        
        # Deshabilitar el botón mientras se procesa
        self.send_button.config(state=tk.DISABLED, text="🔄 Ejecutando...")
        self.status_var.set("🔄 Ejecutando tarea...")
        
        # Ejecutar en un hilo separado para no bloquear la UI
        thread = threading.Thread(target=self.process_query, args=(query,))
        thread.daemon = True
        thread.start()
        
    def process_query(self, query):
        """Procesar la consulta con el agente real"""
        try:
            # Ejecutar el agente real
            self.root.after(0, self.update_status, "🔄 Analizando consulta...")
            
            # Usar el método invoke del agente
            result = self.agent.invoke(query=query)
            
            # Extraer el contenido de la respuesta
            if hasattr(result, 'content'):
                response = result.content
            else:
                response = str(result)
            
            # Mostrar la respuesta en el hilo principal
            self.root.after(0, self.show_response, f"✅ Tarea completada:\n\n{response}")
            
        except Exception as e:
            error_msg = f"❌ Error al ejecutar la tarea:\n\n{str(e)}\n\n"
            error_msg += "🔍 Posibles causas:\n"
            error_msg += "• Problema con la API de Google\n"
            error_msg += "• Elemento no encontrado en la interfaz\n"
            error_msg += "• Aplicación no disponible\n"
            error_msg += "• Conexión a internet\n\n"
            error_msg += "💡 Intenta reformular la consulta o verifica que la aplicación esté disponible."
            
            self.root.after(0, self.show_response, error_msg)
            
    def update_status(self, status):
        """Actualizar el estado en el hilo principal"""
        self.status_var.set(status)
        
    def show_response(self, response):
        """Mostrar la respuesta del agente"""
        self.add_response(response)
        
        # Rehabilitar el botón
        self.send_button.config(state=tk.NORMAL, text="🚀 Enviar (Ctrl+Enter)")
        self.status_var.set("✅ Listo para nueva consulta")
        
    def run(self):
        """Ejecutar la aplicación"""
        try:
            # Centrar la ventana
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
            
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """Función principal"""
    try:
        print("🪟 Iniciando Windows-Use AI Assistant (Versión Funcional)...")
        app = WorkingFloatingWidget()
        app.run()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        messagebox.showerror("Error", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()