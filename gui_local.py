import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import os
import sys
import time
import subprocess
import pyautogui
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class LocalAgent:
    """Agente local que ejecuta acciones básicas sin API externa"""
    
    def __init__(self):
        # Configurar pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
    def invoke(self, query: str):
        """Procesar consulta y ejecutar acciones locales"""
        query_lower = query.lower()
        response = f"🔄 Procesando: {query}\n\n"
        
        try:
            # Abrir aplicaciones
            if 'bloc de notas' in query_lower or 'notepad' in query_lower:
                subprocess.Popen(['notepad'])
                response += "✅ Abriendo Bloc de notas...\n"
                time.sleep(1)
                
            elif 'chrome' in query_lower:
                try:
                    subprocess.Popen(['chrome'])
                    response += "✅ Abriendo Google Chrome...\n"
                    time.sleep(2)
                    
                    # Si menciona un sitio web
                    if 'google.com' in query_lower:
                        subprocess.Popen(['chrome', 'https://www.google.com'])
                        response += "✅ Navegando a Google.com\n"
                    elif 'mercadona.es' in query_lower:
                        subprocess.Popen(['chrome', 'https://www.mercadona.es'])
                        response += "✅ Navegando a Mercadona.es\n"
                        time.sleep(3)
                        
                        # Si menciona código postal
                        if '17116' in query:
                            response += "🔍 Buscando campo de código postal...\n"
                            time.sleep(2)
                            # Intentar hacer clic en el campo de código postal
                            try:
                                # Buscar texto "código postal" en la pantalla
                                pyautogui.click(500, 400)  # Posición aproximada
                                time.sleep(1)
                                pyautogui.write('17116')
                                response += "✅ Código postal 17116 introducido\n"
                                time.sleep(1)
                                pyautogui.press('enter')
                                response += "✅ Presionado Enter\n"
                            except Exception as e:
                                response += f"⚠️ No se pudo introducir el código postal automáticamente: {e}\n"
                                
                except FileNotFoundError:
                    response += "❌ Chrome no encontrado. Intentando con navegador por defecto...\n"
                    if 'google.com' in query_lower:
                        os.system('start https://www.google.com')
                    elif 'mercadona.es' in query_lower:
                        os.system('start https://www.mercadona.es')
                    
            elif 'calculadora' in query_lower or 'calc' in query_lower:
                subprocess.Popen(['calc'])
                response += "✅ Abriendo Calculadora...\n"
                
            elif 'explorador' in query_lower or 'explorer' in query_lower:
                subprocess.Popen(['explorer'])
                response += "✅ Abriendo Explorador de archivos...\n"
                
            elif 'cmd' in query_lower or 'terminal' in query_lower:
                subprocess.Popen(['cmd'])
                response += "✅ Abriendo Terminal (CMD)...\n"
                
            # Acciones de escritura
            elif 'escribe' in query_lower or 'escribir' in query_lower:
                # Extraer texto a escribir
                if '"' in query:
                    text_to_write = query.split('"')[1]
                    time.sleep(1)
                    pyautogui.write(text_to_write)
                    response += f"✅ Texto escrito: {text_to_write}\n"
                else:
                    response += "⚠️ Para escribir texto, usa comillas: escribe \"tu texto aquí\"\n"
                    
            # Acciones de teclado
            elif 'enter' in query_lower or 'intro' in query_lower:
                pyautogui.press('enter')
                response += "✅ Tecla Enter presionada\n"
                
            elif 'tab' in query_lower:
                pyautogui.press('tab')
                response += "✅ Tecla Tab presionada\n"
                
            elif 'escape' in query_lower or 'esc' in query_lower:
                pyautogui.press('escape')
                response += "✅ Tecla Escape presionada\n"
                
            # Captura de pantalla
            elif 'captura' in query_lower or 'screenshot' in query_lower:
                screenshot = pyautogui.screenshot()
                screenshot.save('captura.png')
                response += "✅ Captura de pantalla guardada como 'captura.png'\n"
                
            # Información del sistema
            elif 'hora' in query_lower or 'tiempo' in query_lower:
                current_time = time.strftime("%H:%M:%S - %d/%m/%Y")
                response += f"🕐 Hora actual: {current_time}\n"
                
            else:
                response += "🤔 No reconozco esa acción específica.\n\n"
                response += "Acciones disponibles:\n"
                response += "• Abrir aplicaciones: 'abre bloc de notas', 'abre chrome', 'abre calculadora'\n"
                response += "• Navegar web: 'abre chrome y ve a google.com'\n"
                response += "• Escribir texto: 'escribe \"hola mundo\"'\n"
                response += "• Teclas: 'presiona enter', 'presiona tab'\n"
                response += "• Captura: 'toma una captura de pantalla'\n"
                response += "• Información: 'qué hora es'\n"
                
            response += f"\n⏱️ Acción completada en {time.strftime('%H:%M:%S')}"
            
        except Exception as e:
            response += f"\n❌ Error ejecutando acción: {str(e)}"
            
        # Crear objeto de respuesta compatible
        class LocalResponse:
            def __init__(self, content):
                self.content = content
                
        return LocalResponse(response)

class LocalFloatingWidget:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windows-Use AI Assistant (Local)")
        self.root.geometry("550x750")
        self.root.attributes('-topmost', True)
        
        # Variables de control
        self.agent = LocalAgent()
        self.current_thread = None
        self.is_processing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Título
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        title_frame.columnconfigure(1, weight=1)
        
        title_label = ttk.Label(title_frame, text="🪟 Windows-Use AI (Local)", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # Indicador de estado (siempre verde para local)
        status_indicator = ttk.Label(title_frame, text="● Local", 
                                    foreground="green", font=('Arial', 10))
        status_indicator.grid(row=0, column=1, sticky=tk.E)
        
        # Área de conversación
        chat_frame = ttk.LabelFrame(main_frame, text="Conversación", padding="10")
        chat_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_text = scrolledtext.ScrolledText(
            chat_frame, 
            wrap=tk.WORD, 
            height=22,
            font=('Consolas', 10),
            bg='#f8f9fa',
            fg='#212529',
            state=tk.DISABLED
        )
        self.chat_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Frame de entrada
        input_frame = ttk.LabelFrame(main_frame, text="Nueva consulta", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        # Campo de entrada
        self.query_entry = tk.Text(input_frame, height=4, wrap=tk.WORD, 
                                  font=('Arial', 11), relief=tk.SOLID, borderwidth=1)
        self.query_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.query_entry.bind('<Control-Return>', self.send_query)
        
        # Frame de botones
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        
        # Botón enviar
        self.send_button = ttk.Button(
            button_frame, 
            text="🚀 Ejecutar (Ctrl+Enter)", 
            command=self.send_query
        )
        self.send_button.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        # Botón limpiar
        clear_button = ttk.Button(
            button_frame, 
            text="🧹 Limpiar", 
            command=self.clear_chat
        )
        clear_button.grid(row=0, column=1, padx=(0, 10))
        
        # Checkbox siempre visible
        self.topmost_var = tk.BooleanVar(value=True)
        topmost_check = ttk.Checkbutton(
            button_frame,
            text="📌 Siempre visible",
            variable=self.topmost_var,
            command=self.toggle_topmost
        )
        topmost_check.grid(row=0, column=2, sticky=tk.E)
        
        # Barra de estado
        self.status_var = tk.StringVar(value="✅ Listo - Agente Local Activo")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=('Arial', 9), foreground='green')
        status_label.grid(row=3, column=0, sticky=tk.W)
        
        # Mensaje inicial
        self.add_message("Sistema", self.get_welcome_message(), "system")
        
    def get_welcome_message(self):
        """Obtener mensaje de bienvenida"""
        return """¡Hola! 👋 Soy tu asistente local de Windows-Use.

✅ Estado: Agente Local Activo (Sin dependencias externas)

🎯 Acciones disponibles:

📱 Aplicaciones:
• "Abre el bloc de notas"
• "Abre Chrome"
• "Abre la calculadora"
• "Abre el explorador de archivos"

🌐 Navegación web:
• "Abre Chrome y ve a google.com"
• "Abre Chrome y ve a mercadona.es"

⌨️ Acciones de teclado:
• "Escribe \"tu texto aquí\""
• "Presiona Enter"
• "Presiona Tab"

📸 Utilidades:
• "Toma una captura de pantalla"
• "Qué hora es"

💡 Ejemplo completo:
"Abre Chrome, ve a mercadona.es y pon código postal 17116"

¡Escribe tu consulta y presiona Ctrl+Enter!"""
        
    def toggle_topmost(self):
        self.root.attributes('-topmost', self.topmost_var.get())
        
    def add_message(self, sender, message, msg_type="user"):
        """Agregar mensaje al chat"""
        self.chat_text.config(state=tk.NORMAL)
        
        # Timestamp
        timestamp = time.strftime("%H:%M:%S")
        
        if msg_type == "user":
            self.chat_text.insert(tk.END, f"\n[{timestamp}] 👤 {sender}:\n{message}\n")
            self.chat_text.insert(tk.END, "─" * 60 + "\n")
        elif msg_type == "assistant":
            self.chat_text.insert(tk.END, f"\n[{timestamp}] 🤖 Asistente:\n{message}\n")
            self.chat_text.insert(tk.END, "═" * 60 + "\n")
        elif msg_type == "system":
            self.chat_text.insert(tk.END, f"🔧 Sistema:\n{message}\n")
            self.chat_text.insert(tk.END, "═" * 60 + "\n")
        
        self.chat_text.config(state=tk.DISABLED)
        self.chat_text.see(tk.END)
        
    def clear_chat(self):
        """Limpiar el chat"""
        self.chat_text.config(state=tk.NORMAL)
        self.chat_text.delete(1.0, tk.END)
        self.chat_text.config(state=tk.DISABLED)
        self.add_message("Sistema", "Chat limpiado. ¿Qué acción quieres ejecutar?", "system")
        
    def send_query(self, event=None):
        """Enviar consulta al agente"""
        if self.is_processing:
            messagebox.showwarning("Procesando", "Ya hay una acción en proceso. Espera a que termine.")
            return
            
        query = self.query_entry.get(1.0, tk.END).strip()
        
        if not query:
            messagebox.showwarning("Consulta vacía", "Por favor, escribe una acción a ejecutar.")
            return
            
        # Mostrar consulta del usuario
        self.add_message("Tú", query, "user")
        
        # Limpiar entrada
        self.query_entry.delete(1.0, tk.END)
        
        # Cambiar estado UI
        self.is_processing = True
        self.send_button.config(state=tk.DISABLED, text="🔄 Ejecutando...")
        self.status_var.set("🔄 Ejecutando acción...")
        
        # Ejecutar en hilo separado
        self.current_thread = threading.Thread(target=self.process_query, args=(query,))
        self.current_thread.daemon = True
        self.current_thread.start()
        
    def process_query(self, query):
        """Procesar consulta con el agente local"""
        try:
            # Ejecutar agente local
            result = self.agent.invoke(query=query)
            
            # Mostrar respuesta
            self.root.after(0, self.show_response, result.content)
            
        except Exception as e:
            error_msg = f"❌ Error ejecutando acción: {str(e)}"
            self.root.after(0, self.show_response, error_msg)
            
    def show_response(self, response):
        """Mostrar respuesta en el hilo principal"""
        self.add_message("Asistente", response, "assistant")
        
        # Restaurar UI
        self.is_processing = False
        self.send_button.config(state=tk.NORMAL, text="🚀 Ejecutar (Ctrl+Enter)")
        self.status_var.set("✅ Listo - Agente Local Activo")
        
    def run(self):
        """Ejecutar la aplicación"""
        try:
            # Centrar ventana
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
            
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """Función principal"""
    try:
        print("🪟 Iniciando Windows-Use AI Assistant (Agente Local)...")
        app = LocalFloatingWidget()
        app.run()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        messagebox.showerror("Error", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()