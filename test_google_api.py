import os
from dotenv import load_dotenv

load_dotenv()

def test_google_connection():
    """Probar la conexión con Google Gemini"""
    try:
        print("🔍 Probando conexión con Google Gemini...")
        
        # Verificar API key
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY no encontrada")
            return False
            
        print(f"✅ API Key encontrada: {api_key[:10]}...")
        
        # Probar conexión directa con google-generativeai
        try:
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            
            # Crear modelo
            model = genai.GenerativeModel('gemini-pro')
            
            # Probar una consulta simple
            print("🔄 Enviando consulta de prueba...")
            response = model.generate_content("Di solo 'Hola, funciono correctamente'")
            print(f"✅ Respuesta recibida: {response.text}")
            return True
            
        except Exception as e:
            print(f"❌ Error con google-generativeai: {e}")
            
            # Probar con langchain-google-genai
            try:
                from langchain_google_genai import ChatGoogleGenerativeAI
                
                # Configurar sin proxy
                llm = ChatGoogleGenerativeAI(
                    model='gemini-2.0-flash',
                    google_api_key=api_key,
                    temperature=0.1,
                    timeout=10,
                    transport='rest'  # Usar REST en lugar de gRPC
                )
                
                print("🔄 Probando con LangChain...")
                response = llm.invoke("Di solo 'Hola desde LangChain'")
                print(f"✅ LangChain funciona: {response.content}")
                return True
                
            except Exception as e2:
                print(f"❌ Error con LangChain: {e2}")
                return False
                
    except Exception as e:
        print(f"❌ Error general: {e}")
        return False

if __name__ == "__main__":
    test_google_connection()