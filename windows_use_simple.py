"""
Versión simplificada del agente Windows-Use que evita dependencias problemáticas
"""
import subprocess
import time
import pyautogui
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.language_models.chat_models import BaseChatModel

class SimpleAgent:
    def __init__(self, llm: BaseChatModel, browser='chrome', use_vision=False):
        self.llm = llm
        self.browser = browser
        self.use_vision = use_vision
        
        # Configurar pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 1
        
    def invoke(self, query: str):
        """Procesar una consulta y ejecutar acciones"""
        try:
            # Crear el prompt del sistema
            system_prompt = """Eres un asistente de automatización de Windows. 
            Analiza la consulta del usuario y proporciona una respuesta detallada sobre qué acciones realizarías.
            
            Puedes realizar estas acciones:
            - Abrir aplicaciones
            - Navegar por sitios web
            - Hacer clic en elementos
            - Escribir texto
            - Buscar productos
            - Añadir elementos al carrito
            
            Responde de manera clara y específica sobre los pasos que seguirías."""
            
            # Crear los mensajes
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=query)
            ]
            
            # Obtener respuesta del LLM
            response = self.llm.invoke(messages)
            
            # Intentar ejecutar acciones básicas basadas en la consulta
            self._execute_basic_actions(query)
            
            return response
            
        except Exception as e:
            # Crear una respuesta de error
            class ErrorResponse:
                def __init__(self, error_msg):
                    self.content = f"Error al procesar la consulta: {error_msg}"
            
            return ErrorResponse(str(e))
    
    def _execute_basic_actions(self, query: str):
        """Ejecutar acciones básicas basadas en palabras clave"""
        query_lower = query.lower()
        
        try:
            # Abrir Chrome si se menciona
            if 'chrome' in query_lower and ('abre' in query_lower or 'abrir' in query_lower):
                subprocess.Popen(['chrome'])
                time.sleep(3)
            
            # Abrir sitio web específico
            if 'mercadona.es' in query_lower:
                subprocess.Popen(['chrome', 'https://www.mercadona.es'])
                time.sleep(5)
                
                # Si menciona código postal, intentar interactuar
                if '17116' in query:
                    time.sleep(2)
                    # Buscar campo de código postal y escribir
                    # Esto es una aproximación básica
                    pyautogui.click(400, 300)  # Posición aproximada
                    time.sleep(1)
                    pyautogui.write('17116')
                    time.sleep(1)
                    pyautogui.press('enter')
            
            # Abrir Bloc de notas
            if 'bloc de notas' in query_lower or 'notepad' in query_lower:
                subprocess.Popen(['notepad'])
                time.sleep(2)
                
        except Exception as e:
            print(f"Error ejecutando acciones básicas: {e}")
    
    def print_response(self, query: str):
        """Método de compatibilidad con la interfaz original"""
        result = self.invoke(query)
        print(result.content if hasattr(result, 'content') else str(result))