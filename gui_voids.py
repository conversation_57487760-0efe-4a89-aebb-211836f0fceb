import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import os
import sys
import time
from dotenv import load_dotenv
from voids_llm import VoidsLLM

# Cargar variables de entorno
load_dotenv()

class VoidsFloatingWidget:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windows-Use AI Assistant (Voids API)")
        self.root.geometry("550x750")
        self.root.attributes('-topmost', True)
        
        # Variables de control
        self.agent = None
        self.current_thread = None
        self.is_processing = False
        
        # Intentar inicializar el agente
        self.init_agent()
        self.setup_ui()
        
    def init_agent(self):
        """Inicializar el agente con la API de voids.top"""
        try:
            print("🔄 Inicializando agente con Voids API...")
            
            # Crear el LLM personalizado
            llm = VoidsLLM(
                model_name="gpt-4.1",
                temperature=0.1,
                max_tokens=2000,
                timeout=30
            )
            
            # Probar el LLM
            test_response = llm.invoke("Di solo 'Test OK'")
            print(f"✅ LLM Test: {test_response.content}")
            
            # Importar el agente de Windows-Use
            from windows_use.agent import Agent
            self.agent = Agent(llm=llm, browser='chrome', use_vision=False)
            self.agent_status = "✅ Agente Windows-Use inicializado con Voids API (GPT-4.1)"
            
        except Exception as e:
            self.agent_status = f"❌ Error al inicializar agente: {str(e)}"
            self.agent = None
            print(f"Error: {e}")
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Título con estado
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        title_frame.columnconfigure(1, weight=1)
        
        title_label = ttk.Label(title_frame, text="🪟 Windows-Use AI", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # Indicador de estado
        status_color = "green" if self.agent else "red"
        status_text = "● GPT-4.1" if self.agent else "● Error"
        self.status_indicator = ttk.Label(title_frame, text=status_text, 
                                         foreground=status_color, font=('Arial', 10))
        self.status_indicator.grid(row=0, column=1, sticky=tk.E)
        
        # Área de conversación
        chat_frame = ttk.LabelFrame(main_frame, text="Conversación", padding="10")
        chat_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_text = scrolledtext.ScrolledText(
            chat_frame, 
            wrap=tk.WORD, 
            height=22,
            font=('Consolas', 10),
            bg='#f8f9fa',
            fg='#212529',
            state=tk.DISABLED
        )
        self.chat_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Frame de entrada
        input_frame = ttk.LabelFrame(main_frame, text="Nueva consulta", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        # Campo de entrada
        self.query_entry = tk.Text(input_frame, height=4, wrap=tk.WORD, 
                                  font=('Arial', 11), relief=tk.SOLID, borderwidth=1)
        self.query_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.query_entry.bind('<Control-Return>', self.send_query)
        
        # Frame de botones
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        
        # Botón enviar
        self.send_button = ttk.Button(
            button_frame, 
            text="🚀 Enviar (Ctrl+Enter)", 
            command=self.send_query
        )
        self.send_button.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        # Botón cancelar
        self.cancel_button = ttk.Button(
            button_frame, 
            text="⏹️ Cancelar", 
            command=self.cancel_query,
            state=tk.DISABLED
        )
        self.cancel_button.grid(row=0, column=1, padx=(0, 10))
        
        # Botón limpiar
        clear_button = ttk.Button(
            button_frame, 
            text="🧹 Limpiar", 
            command=self.clear_chat
        )
        clear_button.grid(row=0, column=2, padx=(0, 10))
        
        # Checkbox siempre visible
        self.topmost_var = tk.BooleanVar(value=True)
        topmost_check = ttk.Checkbutton(
            button_frame,
            text="📌 Siempre visible",
            variable=self.topmost_var,
            command=self.toggle_topmost
        )
        topmost_check.grid(row=0, column=3, sticky=tk.E)
        
        # Barra de estado
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_var = tk.StringVar(value="Inicializando...")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                font=('Arial', 9), foreground='gray')
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Barra de progreso
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # Mensaje inicial
        self.add_message("Sistema", self.get_welcome_message(), "system")
        
    def get_welcome_message(self):
        """Obtener mensaje de bienvenida"""
        if self.agent:
            return f"""¡Hola! 👋 Soy tu asistente de Windows-Use con GPT-4.1.

✅ Estado: {self.agent_status}

🎯 Puedo automatizar tareas reales en Windows:

📱 Aplicaciones:
• "Abre el Bloc de notas"
• "Abre Chrome"
• "Abre la calculadora"

🌐 Navegación web:
• "Abre Chrome y ve a google.com"
• "Abre Chrome, ve a mercadona.es, pon código postal 17116 y busca leche"

⌨️ Automatización avanzada:
• "Escribe un email en Gmail"
• "Busca productos en Amazon"
• "Rellena formularios automáticamente"

🧠 Powered by GPT-4.1 - Entiendo lenguaje natural y ejecuto acciones reales.

¡Escribe tu consulta y presiona Ctrl+Enter!"""
        else:
            return f"""❌ Error de inicialización

{self.agent_status}

Para solucionar:
1. Verifica tu conexión a internet
2. Asegúrate de tener todas las dependencias instaladas
3. Reinicia la aplicación

Estado actual: No disponible"""
        
    def toggle_topmost(self):
        self.root.attributes('-topmost', self.topmost_var.get())
        
    def add_message(self, sender, message, msg_type="user"):
        """Agregar mensaje al chat"""
        self.chat_text.config(state=tk.NORMAL)
        
        # Timestamp
        timestamp = time.strftime("%H:%M:%S")
        
        if msg_type == "user":
            self.chat_text.insert(tk.END, f"\n[{timestamp}] 👤 {sender}:\n{message}\n")
            self.chat_text.insert(tk.END, "─" * 60 + "\n")
        elif msg_type == "assistant":
            self.chat_text.insert(tk.END, f"\n[{timestamp}] 🤖 GPT-4.1:\n{message}\n")
            self.chat_text.insert(tk.END, "═" * 60 + "\n")
        elif msg_type == "system":
            self.chat_text.insert(tk.END, f"🔧 Sistema:\n{message}\n")
            self.chat_text.insert(tk.END, "═" * 60 + "\n")
        elif msg_type == "error":
            self.chat_text.insert(tk.END, f"\n[{timestamp}] ❌ Error:\n{message}\n")
            self.chat_text.insert(tk.END, "═" * 60 + "\n")
        
        self.chat_text.config(state=tk.DISABLED)
        self.chat_text.see(tk.END)
        
    def clear_chat(self):
        """Limpiar el chat"""
        self.chat_text.config(state=tk.NORMAL)
        self.chat_text.delete(1.0, tk.END)
        self.chat_text.config(state=tk.DISABLED)
        self.add_message("Sistema", "Chat limpiado. ¿En qué puedo ayudarte?", "system")
        
    def send_query(self, event=None):
        """Enviar consulta al agente"""
        if self.is_processing:
            messagebox.showwarning("Procesando", "Ya hay una consulta en proceso. Espera o cancela la actual.")
            return
            
        query = self.query_entry.get(1.0, tk.END).strip()
        
        if not query:
            messagebox.showwarning("Consulta vacía", "Por favor, escribe una consulta.")
            return
            
        if not self.agent:
            messagebox.showerror("Agente no disponible", 
                               "El agente no está inicializado. Verifica la configuración.")
            return
            
        # Mostrar consulta del usuario
        self.add_message("Tú", query, "user")
        
        # Limpiar entrada
        self.query_entry.delete(1.0, tk.END)
        
        # Cambiar estado UI
        self.is_processing = True
        self.send_button.config(state=tk.DISABLED, text="🔄 Procesando...")
        self.cancel_button.config(state=tk.NORMAL)
        self.status_var.set("🔄 GPT-4.1 analizando y ejecutando...")
        self.progress.start(10)
        
        # Ejecutar en hilo separado
        self.current_thread = threading.Thread(target=self.process_query, args=(query,))
        self.current_thread.daemon = True
        self.current_thread.start()
        
        # Timeout de seguridad
        self.root.after(60000, self.check_timeout)  # 60 segundos
        
    def process_query(self, query):
        """Procesar consulta con el agente"""
        try:
            # Agregar mensaje de estado
            self.root.after(0, self.add_message, "Sistema", 
                          f"🧠 GPT-4.1 analizando: {query[:50]}{'...' if len(query) > 50 else ''}", "system")
            
            # Ejecutar agente con timeout
            start_time = time.time()
            result = self.agent.invoke(query=query)
            end_time = time.time()
            
            # Extraer respuesta
            if hasattr(result, 'content'):
                response = result.content
            else:
                response = str(result)
            
            # Agregar información de tiempo
            duration = round(end_time - start_time, 2)
            response += f"\n\n⏱️ Tiempo de ejecución: {duration} segundos"
            response += f"\n🧠 Procesado por GPT-4.1 via Voids API"
            
            # Mostrar respuesta
            self.root.after(0, self.show_response, response, False)
            
        except Exception as e:
            error_msg = f"""Error al ejecutar la consulta:

{str(e)}

Posibles causas:
• Timeout de la API (>30s)
• Elemento no encontrado en la interfaz
• Aplicación no disponible
• Problema de conexión con Voids API

💡 Intenta:
• Reformular la consulta
• Verificar que la aplicación esté disponible
• Consultas más simples (ej: "Abre Bloc de notas")"""
            
            self.root.after(0, self.show_response, error_msg, True)
            
    def show_response(self, response, is_error=False):
        """Mostrar respuesta en el hilo principal"""
        if is_error:
            self.add_message("Error", response, "error")
        else:
            self.add_message("GPT-4.1", response, "assistant")
        
        # Restaurar UI
        self.is_processing = False
        self.send_button.config(state=tk.NORMAL, text="🚀 Enviar (Ctrl+Enter)")
        self.cancel_button.config(state=tk.DISABLED)
        self.status_var.set("✅ Listo - GPT-4.1 conectado")
        self.progress.stop()
        
    def cancel_query(self):
        """Cancelar consulta actual"""
        if self.current_thread and self.current_thread.is_alive():
            self.is_processing = False
            self.send_button.config(state=tk.NORMAL, text="🚀 Enviar (Ctrl+Enter)")
            self.cancel_button.config(state=tk.DISABLED)
            self.status_var.set("⚠️ Consulta cancelada por el usuario")
            self.progress.stop()
            
            self.add_message("Sistema", "⚠️ Consulta cancelada por el usuario. El proceso puede continuar en segundo plano.", "system")
        
    def check_timeout(self):
        """Verificar timeout de consulta"""
        if self.is_processing:
            self.cancel_query()
            self.add_message("Sistema", "⏰ Timeout: La consulta tardó más de 60 segundos y fue cancelada.", "error")
        
    def run(self):
        """Ejecutar la aplicación"""
        try:
            # Centrar ventana
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
            
            # Actualizar estado final
            if self.agent:
                self.status_var.set("✅ Listo - GPT-4.1 conectado")
            else:
                self.status_var.set("❌ Error - Agente no disponible")
            
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """Función principal"""
    try:
        print("🪟 Iniciando Windows-Use AI Assistant (Voids API + GPT-4.1)...")
        app = VoidsFloatingWidget()
        app.run()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        messagebox.showerror("Error", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()