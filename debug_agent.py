"""
Script para debuggear la inicialización del agente
"""
import sys
import traceback
from voids_llm import VoidsLLM

def debug_agent_init():
    """Debuggear paso a paso la inicialización del agente"""
    
    print("🔍 Debuggeando inicialización del agente...")
    
    try:
        # Paso 1: Probar VoidsLLM
        print("\n1️⃣ Probando VoidsLLM...")
        llm = VoidsLLM(
            model_name="gpt-4.1",
            temperature=0.1,
            max_tokens=2000,
            timeout=30
        )
        print("✅ VoidsLLM creado correctamente")
        
        # Paso 2: Probar invocación del LLM
        print("\n2️⃣ Probando invocación del LLM...")
        test_response = llm.invoke("Di solo 'Test OK'")
        print(f"✅ LLM responde: {test_response.content}")
        
        # Paso 3: Intentar importar windows_use
        print("\n3️⃣ Importando windows_use.agent...")
        from windows_use.agent import Agent
        print("✅ Importación exitosa")
        
        # Paso 4: Crear el agente
        print("\n4️⃣ Creando agente...")
        agent = Agent(llm=llm, browser='chrome', use_vision=False)
        print("✅ Agente creado correctamente")
        
        # Paso 5: Probar el agente
        print("\n5️⃣ Probando agente con consulta simple...")
        result = agent.invoke(query="abre el bloc de notas")
        print(f"✅ Agente funciona: {result.content if hasattr(result, 'content') else str(result)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error en el paso actual:")
        print(f"Tipo de error: {type(e).__name__}")
        print(f"Mensaje: {str(e)}")
        print(f"\nTraceback completo:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_agent_init()