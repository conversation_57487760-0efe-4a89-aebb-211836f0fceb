import requests
import json
from dotenv import load_dotenv

load_dotenv()

def test_voids_api():
    """Probar la API de voids.top"""
    try:
        print("🔍 Probando conexión con API voids.top...")
        
        url = "https://api.voids.top/v1/chat/completions"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer voids"  # Token por defecto según documentación
        }
        
        data = {
            "model": "gpt-4.1",
            "messages": [
                {
                    "role": "user",
                    "content": "Di solo 'Hola, funciono correctamente'"
                }
            ],
            "max_tokens": 50,
            "temperature": 0.1
        }
        
        print("🔄 Enviando consulta de prueba...")
        response = requests.post(url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            message = result['choices'][0]['message']['content']
            print(f"✅ API funciona correctamente: {message}")
            return True
        else:
            print(f"❌ Error HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_voids_api()