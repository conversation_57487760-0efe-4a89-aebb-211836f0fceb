"""
Wrapper para usar la API de voids.top con <PERSON><PERSON><PERSON><PERSON>
"""
import requests
import json
from typing import Any, List, Optional, Dict
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.outputs import Chat<PERSON>eneration, ChatResult
from langchain_core.callbacks.manager import CallbackManagerForLLMRun
from pydantic import Field

class VoidsLLM(BaseChatModel):
    """LLM personalizado para usar la API de voids.top"""
    
    model_name: str = Field(default="gpt-4.1")
    api_url: str = Field(default="https://api.voids.top/v1/chat/completions")
    temperature: float = Field(default=0.1)
    max_tokens: int = Field(default=2000)
    timeout: int = Field(default=30)
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generar respuesta usando la API de voids.top"""
        
        # Convertir mensajes de LangChain al formato de OpenAI
        api_messages = []
        for message in messages:
            if isinstance(message, HumanMessage):
                api_messages.append({"role": "user", "content": message.content})
            elif isinstance(message, AIMessage):
                api_messages.append({"role": "assistant", "content": message.content})
            elif isinstance(message, SystemMessage):
                api_messages.append({"role": "system", "content": message.content})
        
        # Preparar la solicitud
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer voids"
        }
        
        data = {
            "model": self.model_name,
            "messages": api_messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }
        
        try:
            # Hacer la solicitud
            response = requests.post(
                self.api_url, 
                headers=headers, 
                json=data, 
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Crear la respuesta en formato LangChain
                message = AIMessage(content=content)
                generation = ChatGeneration(message=message)
                return ChatResult(generations=[generation])
            else:
                raise Exception(f"API Error {response.status_code}: {response.text}")
                
        except Exception as e:
            # En caso de error, devolver un mensaje de error
            error_message = AIMessage(content=f"Error al conectar con la API: {str(e)}")
            generation = ChatGeneration(message=error_message)
            return ChatResult(generations=[generation])
    
    @property
    def _llm_type(self) -> str:
        return "voids_llm"
    
    @property
    def _identifying_params(self) -> dict:
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }