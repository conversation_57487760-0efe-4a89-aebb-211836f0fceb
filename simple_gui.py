import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import os
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class SimpleFloatingWidget:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windows-Use AI Assistant")
        self.root.geometry("450x650")
        self.root.attributes('-topmost', True)  # Mantener siempre visible
        
        # Configurar el agente (simplificado por ahora)
        self.setup_ui()
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="🪟 Windows-Use AI Assistant", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Área de respuestas (scrollable)
        response_frame = ttk.LabelFrame(main_frame, text="Conversación", padding="5")
        response_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        response_frame.columnconfigure(0, weight=1)
        response_frame.rowconfigure(0, weight=1)
        
        self.response_text = scrolledtext.ScrolledText(
            response_frame, 
            wrap=tk.WORD, 
            height=20,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg='#212529'
        )
        self.response_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Frame para entrada de texto
        input_frame = ttk.LabelFrame(main_frame, text="Tu consulta", padding="5")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        # Campo de entrada
        self.query_entry = tk.Text(input_frame, height=4, wrap=tk.WORD, font=('Arial', 10))
        self.query_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        self.query_entry.bind('<Control-Return>', self.send_query)
        
        # Frame para botones
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        
        # Botón enviar
        self.send_button = ttk.Button(
            button_frame, 
            text="🚀 Enviar (Ctrl+Enter)", 
            command=self.send_query
        )
        self.send_button.grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        # Botón limpiar
        clear_button = ttk.Button(
            button_frame, 
            text="🧹 Limpiar", 
            command=self.clear_responses
        )
        clear_button.grid(row=0, column=1, padx=(5, 5))
        
        # Checkbox para mantener siempre visible
        self.topmost_var = tk.BooleanVar(value=True)
        topmost_check = ttk.Checkbutton(
            button_frame,
            text="📌 Siempre visible",
            variable=self.topmost_var,
            command=self.toggle_topmost
        )
        topmost_check.grid(row=0, column=2, padx=(10, 0))
        
        # Status bar
        self.status_var = tk.StringVar(value="Listo")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              font=('Arial', 8), foreground='gray')
        status_bar.grid(row=3, column=0, sticky=tk.W, pady=(5, 0))
        
        # Mensaje inicial
        self.add_response("¡Hola! 👋 Soy tu asistente de Windows-Use.\n\n" +
                         "Puedo ayudarte a automatizar tareas en Windows como:\n" +
                         "• Abrir aplicaciones\n" +
                         "• Hacer clic en botones\n" +
                         "• Escribir texto\n" +
                         "• Ejecutar comandos\n" +
                         "• Capturar información de la pantalla\n\n" +
                         "Escribe tu consulta y presiona Ctrl+Enter o haz clic en Enviar.", 
                         is_system=True)
        
    def toggle_topmost(self):
        self.root.attributes('-topmost', self.topmost_var.get())
        
    def add_response(self, text, is_user=False, is_system=False):
        """Agregar texto al área de respuestas"""
        self.response_text.config(state=tk.NORMAL)
        
        if is_user:
            self.response_text.insert(tk.END, f"\n👤 Tú:\n{text}\n")
            self.response_text.insert(tk.END, "─" * 50 + "\n")
        elif is_system:
            self.response_text.insert(tk.END, f"🤖 Asistente:\n{text}\n")
            self.response_text.insert(tk.END, "═" * 50 + "\n")
        else:
            self.response_text.insert(tk.END, f"🤖 Respuesta:\n{text}\n")
            self.response_text.insert(tk.END, "═" * 50 + "\n")
        
        self.response_text.config(state=tk.DISABLED)
        self.response_text.see(tk.END)
        
    def clear_responses(self):
        """Limpiar el área de respuestas"""
        self.response_text.config(state=tk.NORMAL)
        self.response_text.delete(1.0, tk.END)
        self.response_text.config(state=tk.DISABLED)
        self.add_response("Área de conversación limpiada. ¿En qué puedo ayudarte?", is_system=True)
        
    def send_query(self, event=None):
        """Enviar consulta al agente"""
        query = self.query_entry.get(1.0, tk.END).strip()
        
        if not query:
            messagebox.showwarning("Advertencia", "Por favor, escribe una consulta.")
            return
            
        # Mostrar la consulta del usuario
        self.add_response(query, is_user=True)
        
        # Limpiar el campo de entrada
        self.query_entry.delete(1.0, tk.END)
        
        # Deshabilitar el botón mientras se procesa
        self.send_button.config(state=tk.DISABLED, text="🔄 Procesando...")
        self.status_var.set("Procesando consulta...")
        
        # Ejecutar en un hilo separado para no bloquear la UI
        thread = threading.Thread(target=self.process_query, args=(query,))
        thread.daemon = True
        thread.start()
        
    def process_query(self, query):
        """Procesar la consulta en un hilo separado"""
        try:
            # Por ahora, simulamos el procesamiento
            # Aquí es donde integrarías el agente real cuando esté funcionando
            
            import time
            time.sleep(2)  # Simular procesamiento
            
            # Respuesta simulada
            response = f"He recibido tu consulta: '{query}'\n\n"
            response += "🔧 Estado: Preparando para ejecutar la tarea...\n"
            response += "⚠️ Nota: La integración completa con Windows-Use está en desarrollo.\n"
            response += "📋 Próximos pasos:\n"
            response += "  1. Analizar la solicitud\n"
            response += "  2. Identificar elementos de la interfaz\n"
            response += "  3. Ejecutar acciones automatizadas\n"
            response += "  4. Reportar resultados\n\n"
            response += "💡 Mientras tanto, puedes probar consultas como:\n"
            response += "  • 'Abre el Bloc de notas'\n"
            response += "  • 'Toma una captura de pantalla'\n"
            response += "  • 'Escribe un texto en el documento activo'"
            
            # Mostrar la respuesta en el hilo principal
            self.root.after(0, self.show_response, response)
            
        except Exception as e:
            error_msg = f"❌ Error al procesar la consulta:\n{str(e)}\n\n"
            error_msg += "🔍 Detalles técnicos:\n"
            error_msg += "La integración completa requiere Python 3.13+ y todas las dependencias.\n"
            error_msg += "Este es un widget de demostración."
            self.root.after(0, self.show_response, error_msg)
            
    def show_response(self, response):
        """Mostrar la respuesta del agente"""
        self.add_response(response)
        
        # Rehabilitar el botón
        self.send_button.config(state=tk.NORMAL, text="🚀 Enviar (Ctrl+Enter)")
        self.status_var.set("Listo")
        
    def run(self):
        """Ejecutar la aplicación"""
        try:
            # Centrar la ventana
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
            
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """Función principal"""
    try:
        print("🪟 Iniciando Windows-Use AI Assistant (GUI)...")
        app = SimpleFloatingWidget()
        app.run()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        messagebox.showerror("Error", f"Error al iniciar la aplicación: {e}")

if __name__ == "__main__":
    main()